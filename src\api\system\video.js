/*
 * @Date: 2025-07-07 09:26:36
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-25 14:17:01
 * @Description:
 * @FilePath: \src\api\system\video.js
 */
import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/videoDevice/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getListDept = (current, size, params) => {
  return request({
    url: '/api/videoDevice/management',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getVideoDetail = (id) => {
  return request({
    url: '/api/videoDevice/detail/'+id,
    method: 'get',
  })
}

export const syncVideoList = () => {
  return request({
    url: '/api/videoDevice/batchSync',
    method: 'post',
  })
}

export const syncVideoOne = (id) => {
  return request({
    url: '/api/videoDevice/singleSync/'+id,
    method: 'post',
  })
}


export const bindVideo = (id,deptId) => {
  return request({
    url: '/api/videoDevice/'+id+'/'+deptId,
    method: 'post'
  })
}
// export const bindVideo = (data) => {
//   return request({
//     url: '/api/admin/monitor/deviceChannel/bind',
//     method: 'post',
//     data
//   })
// }

export const getVideoList = (deptId) => {
  return request({
    url: '/api/admin/monitor/deviceChannel/list',
    method: 'get',
    params: {
      deptId
    }
  })
}

export const getDeviceChannel = (gbId) => {
  return request({
    url: '/api/admin/monitor/deviceChannel/play',
    method: 'get',
    params: {
      gbId
    }
  })
}

export const videoControl = (data) => {
  return request({
    url: '/api/videoControl/controlVerticalAndHorizontal',
    method: 'post',
    data
  })
}


export const getDict = () => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=alarm_rule_type',
    method: 'get',
  })
}

export const bindRuleType = (params) => {
  return request({
    url: '/api/videoDevice/bindRuleType',
    method: 'post',
    params
  })
}
