<!--
 * @Date: 2025-08-04
 * @Description: Tab切换子组件1
-->
<template>
  <div class="tab-component">
    <div class="component-header">
      <h2>组件1</h2>
    </div>
    <div class="component-content">
      <p>这是第一个子组件的内容</p>
      <div class="content-box">
        <h3>功能区域</h3>
        <ul>
          <li>功能项目 1</li>
          <li>功能项目 2</li>
          <li>功能项目 3</li>
        </ul>
      </div>
      <div class="action-area">
        <button class="btn-primary">主要操作</button>
        <button class="btn-secondary">次要操作</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabComponent1',
  data() {
    return {
      // 组件1的数据
    }
  },
  methods: {
    // 组件1的方法
  }
}
</script>

<style scoped>
.tab-component {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  min-height: 400px;
}

.component-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.component-header h2 {
  color: #007bff;
  margin: 0;
}

.component-content {
  line-height: 1.6;
}

.content-box {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-box h3 {
  color: #333;
  margin-top: 0;
}

.content-box ul {
  margin: 10px 0;
  padding-left: 20px;
}

.content-box li {
  margin: 5px 0;
}

.action-area {
  margin-top: 20px;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
  font-size: 14px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}
</style>
