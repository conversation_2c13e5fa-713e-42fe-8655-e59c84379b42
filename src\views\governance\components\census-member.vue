<!--
 * @Author: zhouwj83
 * @Date: 2021-06-22 19:02:18
 * @LastEditors: chenz76
 * @LastEditTime: 2022-08-02 11:05:23
 * @Description: 户籍管理-成员管理
-->

<template>
  <el-drawer :title="title" :visible.sync="showDrawer" direction="rtl" append-to-body size="70%" @closed="handleClose">
    <div style="padding: 20px">
      <avue-crud
        ref="childCrud"
        v-model="form"
        :option="option"
        :table-loading="loading"
        :data="data"
        :permission="permissionList"
        :before-open="beforeOpen"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @selection-change="selectionChange"
        @refresh-change="onLoad"
      >
        <template slot="menuLeft">
          <el-button type="danger" v-if="permission.census_member_delete" size="small" icon="el-icon-delete" plain @click="handleDelete">删 除 </el-button>
        </template>
        <!-- <template slot="contract" slot-scope="{ row }">
          <el-tag v-if="row.contract">{{ row.contract }}</el-tag>
          <span v-else>-</span>
        </template> -->
        <template slot="userIdForm">
          <el-select v-model="form.userId" placeholder="请选择 成员" filterable remote :remote-method="searchUser"  v-load-more="loadMore" @change="userSelectChange">
            <el-option v-for="(item, index) in userOptions" :key="index" :value="item.id" :label="item.realName">
              <div class="census-user-option">
                <img class="top-bar__img" :src="item.avatar" onerror="this.src='/img/defaultAvatarImg.png'" />
                <span class="content">{{ item.realName + (item.phone ? ' - ' + item.phone : '') }}</span>
              </div>
            </el-option>
          </el-select>
        </template>
      </avue-crud>
    </div>
  </el-drawer>
</template>

<script>
import { getMemberList, getVillagerDetail, removeVillager, addVillager, updateVillager } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import { cardId } from '@/util/validate'
import { keyTrim } from '@/util/util'
// 提交前需要处理空格的form字段
const keys = ['remark']

export default {
  directives: {
    'load-more': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector( '.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function() {
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
        });
      }
    }
  },
  data() {
    // var checkMobile = (rule, value, callback) => {
    //   if (value && value.length > 0 && !isMobile(value)) {
    //     callback(new Error('手机号码格式错误'))
    //   } else {
    //     callback()
    //   }
    // }
    var checkIdNo = (rule, value, callback) => {
      if (!value) {
        return callback();
      } else if (cardId(value)[0]) {
        return callback(new Error("身份证号格式错误"));
      } else {
        return callback();
      }
    };
    // const checkRealName = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error("请输入姓名"));
    //   } else if (!noSpace(value)) {
    //     return callback(new Error("姓名不能含有空格"));
    //   } else if (!validatename(value)) {
    //     return callback(new Error("姓名必须是中文,长度2-20"));
    //   } else {
    //     callback();
    //   }
    // };
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length>1 && value.length<21) {
        if (!value.trim()) {
          callback(new Error('姓名不能为纯空格'))
        }
      }else{
        callback(new Error('姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      showDrawer: false,

      selectOptions: {
        current: 1,
        size: 20
      },
      userOptions: [],
      baseInfo: {},

      form: {},
      loading: true,
      data: [],
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        selection: true,
        viewBtn: true,
        searchBtn: false,
        dialogCustomClass: 'census-member-drawer',
        column: [
          // {
          //   label: '姓名',
          //   prop: 'id',
          //   width: 110,
          //   formslot: true,
          //   rules: [
          //     {
          //       required: true,
          //       message: '请选择成员',
          //       trigger: 'change'
          //     }
          //   ],
          //   hide: true,
          //   showColumn: false,
          //   viewDisplay: false,
          //   span: 12
          // },
          {
            label: '姓名',
            prop: 'realName',
            maxlength: 20,
            showWordLimit: true,
            width: 110,
            rules: [
              {
                required: true,
                validator: checkName,
                trigger: "blur",
              },
            ],
          },
          {
            label: '户籍关系',
            prop: 'relationship',
            placeholder: '请选择 户主或与户主关系',
            width: 110,
            slot: true,
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=householder_relationship',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            rules: [
              {
                required: true,
                message: '请选择与户主关系',
                trigger: 'change'
              }
            ],
            span: 12
          },
          {
            label: '性别',
            prop: 'sex',
            width: 90,
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=sex',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            rules: [],
            span: 12
          },
          {
            label: '联系方式',
            prop: 'contract',
            type: 'input',
            maxlength: 20,
            showWordLimit:true,
            // rules: [
            //   {
            //     validator: checkMobile,
            //     trigger: 'change'
            //   }
            // ],
            span: 12
          },
          {
            label: '身份证',
            prop: 'idno',
            type: 'input',
            maxlength: 20,
            rules: [
              {
                validator: checkIdNo,
                trigger: 'change'
              }
            ],
            span: 12
          },
          {
            label: '民族',
            prop: 'nation',
            width: 120,
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=nation',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            rules: [],
            span: 12
          },
          {
            label: '地址',
            prop: 'address',
            type: 'textarea',
            minRows: 1,
            maxRows: 2,
            maxlength: 250,
            hide: true,
            showWordLimit: true,
            span: 24,
            rules: [],
            overHidden: true
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            minRows: 2,
            maxRows: 4,
            maxlength: 500,
            showWordLimit: true,
            hide: true,
            showColumn: false,
            span: 24
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.census_member_add, false),
        viewBtn: this.vaildData(this.permission.census_member_view, false),
        delBtn: this.vaildData(this.permission.census_member_delete, false),
        editBtn: this.vaildData(this.permission.census_member_edit, false)
      }
    },
    title() {
       return `${this.baseInfo.householderName} - 户籍成员`
    },
    ids() {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    }
  },
  methods: {
    async init(census) {
      this.baseInfo = census
      this.onLoad()
      this.showDrawer = true
    },
    handleClose() {
      this.baseInfo = this.$options.data().baseInfo
      this.selectionClear()
      this.data = []
      this.loading = true
    },
    rowSave(row, done, loading) {
      row = keyTrim(row, keys)

      const _row = { ...row }
      _row.cid = this.baseInfo.id
      addVillager(_row).then(
        () => {
          this.onLoad()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      row = keyTrim(row, keys)
      let _this = this;
      const _row = { ...row }
      _row.cid = this.baseInfo.id;
      updateVillager(_row).then(
        () => {
          this.onLoad();
          //如果是户主，修改标题，刷新父类
          if(_row.relationshipName==='户主'){
            _this.baseInfo.householderName = _row.realName;
            this.$emit('reflashTable')
          }
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return removeVillager(row.id)
        })
        .then(() => {
          this.onLoad()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return removeVillager(this.ids)
        })
        .then(() => {
          this.onLoad()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.childCrud.toggleSelection()
        })
    },
    async beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        let data = {}
        await new Promise((resolve) => {
          getVillagerDetail(this.form.id, type === 'view').then((res) => {
            data = res.data.data
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.childCrud.toggleSelection()
    },
    onLoad() {
      this.loading = true
      getMemberList(this.baseInfo.id).then((res) => {
        this.data = res.data.data
        this.loading = false
        this.selectionClear()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-member-drawer .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}
</style>
