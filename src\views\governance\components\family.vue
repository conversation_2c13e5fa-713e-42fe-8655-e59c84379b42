<!--
 * @Author: linzq33
 * @Date: 2025-08-04 
 * @LastEditors: 
 * @LastEditTime: 
 * @Description: 户籍管理-家庭
-->

<template>
  <div>
    <avue-crud ref="crud" v-model="form" :option="option" :table-loading="loading" :data="data" :page.sync="page"
      :permission="permissionList" :before-open="beforeOpen" @row-update="rowUpdate" @row-save="rowSave"
      @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.census_import" size="small" icon="el-icon-upload2" @click="handleImport">导
          入</el-button>
        <el-button v-if="permission.census_delete" type="danger" size="small" icon="el-icon-delete" plain
          @click="handleDelete">批量删除 </el-button>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="mini" icon="el-icon-user" @click="openMember(scope.row)">家庭成员</el-button>
        <el-button type="text" size="mini" icon="el-icon-delete" @click="$refs.crud.rowDel(scope.row)">删
          除</el-button>
      </template>
    </avue-crud>
    <!-- $refs.censusMember.init(scope.row) -->
    <censusMember ref="censusMember" @reflashTable="reflashTable" />
    <!-- 家庭成员编辑 -->
    <el-dialog :title="dialogTitle" append-to-body :visible.sync="memberBox" width="1200px" @before-close="handleClose">
      <member type="detail" :familyId="familyId" />
    </el-dialog>

    <el-dialog title="户籍数据导入" append-to-body :visible.sync="excelBox" width="555px">
      <avue-form v-if="excelBox" v-model="excelForm" :option="excelOption" :upload-before="uploadBefore"
        :upload-error="uploadError" :upload-after="uploadAfter">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate"> 点击下载<i class="el-icon-download el-icon--right" />
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import { isMobile } from '@/util/validate'
import censusMember from './census-member.vue'
import member from './member.vue'
import axios from 'axios'
import website from '@/config/website'
import { handleDownload } from '@/util/download';
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";

export default {
  components: {
    censusMember, member
  },
  directives: {
    'load-more': {
      bind (el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  props: {
    dept: {
      default: '',
      type: String
    }
  },
  data () {
    var checkMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系方式'))
      } else if (value.length > 0 && !isMobile(value)) {
        callback(new Error('手机号码格式错误'))
      } else {
        callback()
      }
    }
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length > 1 && value.length < 21) {
        if (!value.trim()) {
          callback(new Error('户主姓名不能为纯空格'))
        }
      } else {
        callback(new Error('户主姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      icon: "el-icon-download el-icon--right",
      disableButton: false,
      dialogTitle: '成员',
      memberBox: false,
      familyId: '',
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        labelPosition: 'right',
        viewBtn: true,
        delBtn: false,
        selection: true,
        menuWidth: 330,
        searchMenuSpan: 4,
        labelWidth: 120,
        dialogWidth: 600,
        column: [
          {
            label: '户主姓名',
            prop: 'householderName',
            width: 180,
            type: 'input',
            maxlength: 20,
            showWordLimit: true,
            span: 24,
            search: true,
            searchSpan: 6,
            rules: [
              {
                required: true,
                validator: checkName,
                trigger: "blur",
              }
            ],
          },
          {
            label: "户主联系方式",
            prop: "contract",
            // hide: true, //隐藏列
            span: 24,
            maxlength: 11,
            showWordLimit: true,
            rules: [{ required: true, validator: checkMobile, trigger: 'change' }],
          },
          {
            label: '居住地址',
            prop: 'address',
            type: 'textarea',
            minRows: 3,
            maxRows: 4,
            maxlength: 250,
            showWordLimit: true,
            span: 24,
          }
        ]
      },
      data: [],
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '模板上传',
            prop: 'excelFile',
            type: 'upload',
            drag: true,
            loadText: '模板上传中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            tip: '请上传 .xls,.xlsx 标准格式文件',
            action: '/api/census/import'
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ]
      },
      deptId: ''
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.census_add, false),
        viewBtn: this.vaildData(this.permission.census_view, false),
        delBtn: this.vaildData(this.permission.census_delete, false),
        editBtn: this.vaildData(this.permission.census_edit, false)
      }
    },
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    dept: function (val) {
      this.deptId = val
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
  created () {
  },
  methods: {
    openMember (row) {
      console.log('234', row);
      this.dialogTitle = row.householderName + '的家庭成员'
      this.memberBox = true
      this.familyId = row.id
    },
    handleClose () {
      this.memberBox = false

    },
    rowSave (row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate (row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel (row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleExport () {
      this.$confirm('是否导出户籍数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let downloadUrl = `/api/census/export?${this.website.tokenHeader}=${getToken()}`
        if (this.selectionList.length > 0) {
          downloadUrl += `&ids=${this.ids}`
        }
        const householderName = this.query.householderName || ''
        if (householderName.length > 0) {
          downloadUrl += `&householderName=${householderName}`
        }
        this.icon = "el-icon-loading el-icon--right";
        this.disableButton = true;
        const result = await handleDownload(downloadUrl);
        if (result != null) {
          this.icon = "el-icon-download el-icon--right";
          this.disableButton = false;
        }
      })
    },
    async beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        await new Promise((resolve) => {
          getDetail(this.form.id).then((res) => {
            const data = res.data.data || {}
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    onLoad (page, params = {}) {
      this.loading = true
      params.deptId = this.deptId
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    handleImport () {
      this.excelBox = true
    },
    uploadAfter (res, done) {
      if (!res) {
        this.$message.success('导入成功')
      }
      axios.defaults.timeout = website.timeout
      this.excelForm = {}
      this.excelBox = false
      this.refreshChange()
      done()
    },
    uploadBefore (file, done) {
      axios.defaults.timeout = 600000 //上传前设置超时时间为10分钟
      done()
      return
    },
    uploadError () {
      axios.defaults.timeout = website.timeout
    },
    handleTemplate () {
      exportBlob(`/api/census/module`).then(res => {
        downloadXls(res.data, "户籍导入数据模板.xlsx");
      })
    },
    reflashTable () {
      this.$refs.crud.refreshTable();
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;

  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-dialog .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}

.census-dialog .el-input .el-input__count {
  margin-top: 25px;
}
</style>
