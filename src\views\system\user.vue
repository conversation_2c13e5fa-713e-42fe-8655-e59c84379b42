<!--
 * @Date: 2025-06-25 14:24:02
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 15:09:43
 * @Description:
 * @FilePath: \src\views\system\user.vue
-->
<!--
 * @Date: 2025-06-25 14:24:02
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-01 20:12:42
 * @Description:
 * @FilePath: \src\views\system\user.vue
-->
<template>
  <el-row>
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree ref="tree" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud :option="option" :defaults.sync="defaults" :search.sync="search" :table-loading="loading" :data="data" ref="crud" v-model="form" :permission="permissionList" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" :before-open="beforeOpen" :page.sync="page" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
          <template slot="menuLeft">
            <el-button type="primary" size="small" v-if="permission.user_add && !auditMode" icon="el-icon-plus" @click="$refs.crud.rowAdd()">新 增
            </el-button>
            <el-button type="danger" size="small" plain icon="el-icon-delete" v-if="permission.user_delete && !auditMode" @click="handleDelete">删 除
            </el-button>
            <el-button type="primary" size="small" plain icon="el-icon-refresh-left" v-if="userInfo.role_name.includes('admin') && auditMode" @click="handleAuditBack">返 回
            </el-button>
            <el-button type="info" size="small" plain v-if="permission.user_reset && !auditMode" icon="el-icon-refresh" @click="handleReset">密码重置
            </el-button>

            <el-button type="info" size="small" plain v-if="userInfo.role_name.includes('admin') && !auditMode" icon="el-icon-coordinate" @click="handleLock">账号解封
            </el-button>
            <el-button type="success" size="small" plain v-if="userInfo.role_name.includes('admin') && !auditMode" icon="el-icon-upload2" @click="handleImport">导入
            </el-button>
            <el-button type="warning" size="small" plain v-if="userInfo.role_name.includes('admin') && !auditMode" icon="el-icon-download" @click="handleExport">导出
            </el-button>
          </template>
          <template slot-scope="{ row }" slot="tenantName">
            <el-tag>{{ row.tenantName }}</el-tag>
          </template>
          <template slot-scope="{ row }" slot="roleName">
            <el-tag>{{ row.roleName }}</el-tag>
          </template>
          <template slot-scope="{ row }" slot="isActive">
            <el-tag v-if="row.isActive" type="success">已激活</el-tag>
            <el-tag v-else type="danger">未激活</el-tag>
          </template>
          <template slot-scope="{type}" slot="roleIdForm">
            <el-select v-model="form.roleId" :disabled="type=='view'" clearable multiple placeholder="请选择 所属角色">
              <el-option v-for="item in roleIdList" :key="item.id" :label="item.title" :value="item.key" :disabled="item.disabled">
              </el-option>
            </el-select>
          </template>
          <template slot="menuForm">
            <el-button v-if="userInfo.role_name.includes('administrator')" type="primary" size="small" @click="getInitDepartData">超级管理员加载部门</el-button>
          </template>
        </avue-crud>
        <!-- <el-dialog title="用户角色配置" append-to-body :visible.sync="roleBox" width="345px">
          <el-tree :data="roleGrantList" show-checkbox check-strictly default-expand-all node-key="id" ref="treeRole" :default-checked-keys="roleTreeObj" :props="props">
          </el-tree>

          <span slot="footer" class="dialog-footer">
            <el-button @click="roleBox = false">取 消</el-button>
            <el-button type="primary" @click="submitRole">确 定</el-button>
          </span>
        </el-dialog> -->
        <el-dialog title="用户数据导入" append-to-body :visible.sync="excelBox" width="555px">
          <avue-form :option="excelOption" v-model="excelForm" :upload-after="uploadAfter">
            <template slot="excelTemplate">
              <el-button type="primary" @click="handleTemplate">
                点击下载<i class="el-icon-download el-icon--right"></i>
              </el-button>
            </template>
          </avue-form>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import {
  getList,
  getUser,
  remove,
  update,
  add,
  // grant,
  resetPassword,
  unlock,
  auditPass,
  auditRefuse,
} from "@/api/system/user";
import { exportBlob } from "@/api/common";
import { getRoleTree } from "@/api/system/role";
import { getPostList } from "@/api/system/post";
import { mapGetters } from "vuex";
import { getToken } from "@/util/auth";
import { downloadXls } from "@/util/util";
import { dateNow } from "@/util/date";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import func from "@/util/func";
import { getDeptTree, getDeptUserTree } from "@/api/system/dept";
import { noSpace, checkChinese, isKeyBoardContinuousChar, passWordLimit } from '@/util/validate'
export default {
  data() {
    const validatePassword = (rule, value, callback) => {

      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        const keyBoard = isKeyBoardContinuousChar(value)
        const passWord = passWordLimit(value)
        const chinese = checkChinese(value)
        const isNoSpace = noSpace(value)
        if (value == this.form.repeatPassword) {
          this.$refs.crud.validateField("repeatPassword");
        }
        // console.log(keyBoard,passWord,chinese)
        if (keyBoard === true) {
          callback(new Error('密码不能含有键盘排序'))
        } else if (passWord === false) {
          callback(new Error('至少包含大写字母、小写字母、数字、特殊字符中的三类字符'))
        } else if (isNoSpace === false) {
          callback(new Error('密码不能含有空格'))
        } else if (chinese === true) {
          callback(new Error('密码不能含有中文'))
        } else {
          callback()
        }
      }
    }
    // const validatePass = (rule, value, callback) => {
    //   if (value === "") {
    //     callback(new Error("请输入密码"));
    //   } else {
    //     callback();
    //   }
    // };
    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.form.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      form: {},
      search: {},
      roleBox: false,
      excelBox: false,
      auditMode: false,
      selectionList: [],
      query: {},
      loading: true,
      platformLoading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      platformPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      init: {
        roleTree: [],
        deptTree: [],
      },
      props: {
        label: "title",
        value: "key",
      },
      roleGrantList: [],
      roleTreeObj: [],
      treeGridId: "",
      treeData: [],
      treeOption: {
        nodeKey: "id",
        defaultExpandAll: false,
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      },
      defaults: {},
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        addBtn: false,
        viewBtn: true,
        labelWidth: 120,
        // dialogType: "drawer",
        dialogClickModal: false,
        column: [
          {
            label: "用户姓名",
            prop: "realName",
            search: true,
            display: false,
            maxlength: 20,
          },
          {
            label: "联系方式",
            prop: "phone",
            search: true,
            display: false,
            maxlength: 11,
          },
          {
            label: "所属角色",
            prop: "roleName",
            slot: true,
            display: false,
          },
          {
            label: "所属角色",
            prop: "roleIds",
            multiple: true,
            dataType: "string",
            type: "tree",
            search: true,
            hide: true,
            dicData: [],
            display: false,
            props: {
              label: "title",
            },
            checkStrictly: true,
          },
          {
            label: "所属村镇",
            prop: "deptName",
            slot: true,
            display: false,
          },
          {
            label: "管理范围",
            prop: "manageDeptName",
            slot: true,
            display: false,
          },
          {
            label: "是否激活",
            prop: "isActive",
            slot: true,
            display: false,
          },
          {
            label: "激活时间",
            prop: "activeTime",
            slot: true,
            display: false,
          },
        ],
        group: [
          {
            label: "详细信息",
            prop: "baseInfo",
            icon: "el-icon-user-solid",
            column: [
              {
                label: "用户昵称",
                prop: "name",
                hide: true,
                minlength: 2,
                maxlength: 20,
                rules: [{
                  min: 2,
                  max: 20,
                  message: '用户昵称长度在2到20个字符'
                }],
                showWordLimit: true,
              },
              {
                label: "真实姓名",
                prop: "realName",
                rules: [{
                  required: true,
                  message: "请输入真实姓名",
                  trigger: ["blur", "change"],
                }, {
                  pattern: /^[\u4e00-\u9fa5]{2,20}$/,
                  message: "真实姓名必须为2-20个中文字符",
                  trigger: ["blur", "change"],
                }],

                minlength: 2,
                maxlength: 20,
                showWordLimit: true,
              },
              {
                label: "手机号码",
                prop: "phone",
                rules: [
                  //表单规则
                  {
                    required: true,
                    message: "请填写正确的手机号码",
                    trigger: "blur",
                    pattern: /^1[3456789]\d{9}$/,
                  },
                ],
                maxlength: 11,
                showWordLimit: true,
              },
              {
                label: "政治面貌",
                prop: "politicsId",
                // dicData:[],
                dataType: "string",
                dicUrl: "/api/blade-system/dict/dictionary?code=politics_type",
                type: "select",
                props: {
                  label: "dictValue",
                  value: "dictKey"
                },
              }
            ],
          },
          {
            label: "职责信息",
            prop: "detailInfo",
            icon: "el-icon-s-order",
            column: [
              {
                label: "所属角色",
                prop: "roleId",
                formslot: true,
                // multiple: true,
                // type: "tree",
                // search: true,
                // dataType: "string",
                // dicData: [],
                // props: {
                //   label: "title",
                // },
                // checkStrictly: true,
                // slot: true,
                rules: [
                  {
                    required: true,
                    message: "请选择所属角色",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "所属村镇/部门",
                prop: "deptId",
                type: "tree",
                parent: true,
                dicData: [],
                addDisabled: false,
                dataType: "string",
                hide: true,
                props: {
                  label: "title",
                  value: "id",
                  children: "children",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择所属村镇",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "所属岗位",
                prop: "postId",
                type: "select",
                filter: false,
                multiple: true,
                dataType: "string",
                dicData: [],
                props: {
                  label: "postName",
                  value: "id",
                },
              },
            ],
          },
          {
            label: "账户信息",
            prop: "dutyInfo",
            display: true,
            icon: "el-icon-s-custom",
            column: [
              {
                label: "登录账号",
                prop: "phone",
                addDisabled: true,
                editDisabled: true,
                placeholder: "手机号码作为登录账号",
                rules: [
                  {
                    required: true,
                    message: "请输入账号",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "管理范围",
                prop: "manageDeptId",
                type: "tree",
                dicData: [],
                addDisabled: false,
                parent: true,
                display: true,
                dataType: "string",
                hide: true,
                props: {
                  label: "title",
                  value: "id",
                  children: "children",
                },
                rules: [
                  {
                    required: true,
                    message: "请选择管理范围",
                    trigger: ["blur", "change"],
                  },
                ],
              },
              {
                label: "密码",
                prop: "password",
                hide: true,
                editDisplay: false,
                viewDisplay: false,
                rules: [
                  { min: 8, max: 16, message: '密码长度在8到16个字符' },
                  { required: true, validator: validatePassword, trigger: ["blur", "change"], },
                ],
              },
              {
                label: "确认密码",
                prop: "repeatPassword",
                hide: true,
                editDisplay: false,
                viewDisplay: false,
                rules: [
                  { required: true, validator: validatePass2, trigger: ["blur", "change"], },
                ],
              },
            ],
          }
        ],
      },
      data: [],
      roleIdList: [],
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            loadText: "模板上传中，请稍等",
            span: 24,
            propsHttp: {
              res: "data",
            },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/blade-system/user/import-user",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },
    };
  },
  watch: {
    "form.roleId"(val) {
      console.log(val, "ca");
      const column = this.findObject(this.option.group, "dutyInfo");
      const column1 = this.findObject(this.option.group, "manageDeptId");
      const column2 = this.findObject(this.option.group, "deptId");
      if (!val || val.length == 0) {
        column.display = true;
        column1.display = true;
        column2.parent = true;
        this.roleIdList.forEach(item => {
          item.disabled = false;
        });
        return;
      }
      const convertRoleData = (data, flag) => {
        if (!Array.isArray(data)) return [];
        return data.map(item => {
          let roleItem;
          if (flag === 'keyToTitle') {
            // key转title
            roleItem = this.roleIdList.find(role => role.key === item);
            return roleItem ? roleItem.title : item;
          } else if (flag === 'titleToKey') {
            // title转key
            roleItem = this.roleIdList.find(role => role.title === item);
            return roleItem ? roleItem.key : item;
          }
          return item;
        });
      }

      let labelValue = convertRoleData(val, 'keyToTitle');
      // console.log(labelValue);
      if (labelValue.includes("村干部")) {
        this.roleIdList.forEach(item => {
          if (item.title === "镇干部") {
            item.disabled = true;
          }
        });
      } else if (labelValue.includes("镇干部")) {
        this.roleIdList.forEach(item => {
          if (item.title === "村干部") {
            item.disabled = true;
          }
        });
      } else {
        this.roleIdList.forEach(item => {
          item.disabled = false;
        });
      }


      if (labelValue.some(role => ["超级管理员", "租户管理员", "镇干部", "村干部"].includes(role))) {
        this.roleIdList.forEach(item => {
          if (item.title === "平台服务开通" || item.title === "云智眼支撑人员") {
            item.disabled = true;
          }
        });
      } else if (labelValue.some(role => ["平台服务开通", "云智眼支撑人员"].includes(role))) {
        this.roleIdList.forEach(item => {
          if (item.title === "超级管理员" || item.title === "租户管理员" || item.title === "镇干部" || item.title === "村干部") {
            item.disabled = true;
          }
        });
      } else {
        this.roleIdList.forEach(item => {
          item.disabled = false;
        });
      }
      // const column = this.findObject(this.option.group, "manageDeptId");

      if (labelValue.length == 1 && labelValue.includes("村民")) {
        column.display = false;
      } else {
        column.display = true;
      }


      if (labelValue.some(role => ["租户管理员", "镇干部", "平台服务开通", "云智眼支撑人员"].includes(role))) {
        column1.display = true;
      } else {
        this.form.manageDeptId = ""
        column1.display = false;
      }

      // 仅有村干部或仅有村民或仅有村干部和村民角色

      if (labelValue.every(role => ["村干部", "村民"].includes(role)) && labelValue.length > 0) {
        // column2.parent = false;
        console.log(column2.dicData, "column2.data");
        let deptIdList = column2.dicData;

        // 递归为树形数据添加disabled属性
        const addDisabledRecursively = (nodes) => {
          if (!Array.isArray(nodes)) return nodes;

          return nodes.map(node => {
            const newNode = {
              ...node,
              disabled: node.category < 5
            };

            // 如果有子节点，递归处理
            if (node.children && Array.isArray(node.children)) {
              newNode.children = addDisabledRecursively(node.children);
            }

            return newNode;
          });
        };

        // 应用递归函数
        column2.dicData = addDisabledRecursively(deptIdList);
        console.log(column2.dicData, "updated column2.dicData with disabled");
      } else {
        // column2.parent = true;
        let deptIdList = column2.dicData;
        const addDisabledRecursively = (nodes) => {
          if (!Array.isArray(nodes)) return nodes;

          return nodes.map(node => {
            const newNode = {
              ...node,
              disabled: false
            };

            // 如果有子节点，递归处理
            if (node.children && Array.isArray(node.children)) {
              newNode.children = addDisabledRecursively(node.children);
            }

            return newNode;
          });
        };

        // 应用递归函数
        column2.dicData = addDisabledRecursively(deptIdList);
        console.log(column2.dicData, "updated column2.dicData with disabled");
      }


    },
 
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.user_add, false),
        viewBtn: this.vaildData(this.permission.user_view, false),
        delBtn: this.vaildData(this.permission.user_delete, false),
        editBtn: this.vaildData(this.permission.user_edit, false),
      };
    },
    platformPermissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: this.vaildData(this.permission.user_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  mounted() {
    // 非租户模式默认加载管理组数据
    this.initData();
    // console.log(this.userInfo);
  },
  methods: {
    nodeClick(data) {
      this.treeGridId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    initData() {
      getDeptUserTree().then(res => {
        this.treeData = res.data.data;
      });
      // getRoleTree(this.userInfo.tenant_id).then(res => {
      //   const column = this.findObject(this.option.group, "roleId");
      //   column.dicData = res.data.data;
      //   const column1 = this.findObject(this.option.column, "roleIds");
      //   column1.dicData = res.data.data;
      // });
      getRoleTree().then(res => {
        this.roleIdList = res.data.data.map(item => ({
          ...item,
          disabled: false
        }));
        const column = this.findObject(this.option.column, "roleIds");
        column.dicData = res.data.data;
      });
      getPostList().then(res => {
        const column = this.findObject(this.option.group, "postId");
        column.dicData = res.data.data;
      });
      // console.log(this.userInfo.role_name.includes('administrator'));
      if (!this.userInfo.role_name.includes('administrator')) {
        this.getInitDepartData()
      }

    },

    getInitDepartData() {

      getDeptTree(false).then(res => {
        const column = this.findObject(this.option.group, "deptId");
        column.dicData = res.data.data;
      });
      getDeptTree(true).then(res => {
        const column = this.findObject(this.option.group, "manageDeptId");
        column.dicData = res.data.data;
      });
    },

    // submitRole() {
    //   const roleList = this.$refs.treeRole.getCheckedKeys().join(",");
    //   grant(this.ids, roleList).then(() => {
    //     this.roleBox = false;
    //     this.$message({
    //       type: "success",
    //       message: "操作成功!",
    //     });
    //     this.onLoad(this.page);
    //   });
    // },
    rowSave(row, done, loading) {
      // console.log({row});
      let params = {
        name: row.name,
        password: row.password,
        realName: row.realName,
        phone: row.phone,
        deptId: row.deptId,
        politicsId: row.politicsId,
        roleId: row.roleId.join(','),
        postId: row.postId,
        repeatPassword: row.repeatPassword,
        manageDeptId: row.manageDeptId
      }
      add(params).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      // row.deptId = func.join(row.deptId);
      // row.roleId = func.join(row.roleId);
      // row.postId = func.join(row.postId);
      let params = {
        id: row.id,
        name: row.name,
        realName: row.realName,
        phone: row.phone,
        deptId: row.deptId,
        politicsId: row.politicsId,
        roleId: row.roleId.join(','),
        postId: row.postId,
        manageDeptId: row.manageDeptId

      }
      update(params).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();

        }
      )
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchReset() {
      this.query = {};
      console.log(this.$refs.tree);
      this.page.currentPage = 1;
      this.$refs.tree.setCurrentKey()
      this.treeGridId = "";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAudit() {
      this.auditMode = true;
      this.onLoad(this.page, this.query);
    },
    handleAuditPass() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据通过审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return auditPass(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAuditRefuse() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据拒绝审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return auditRefuse(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleAuditBack() {
      this.auditMode = false;
      this.onLoad(this.page, this.query);
    },
    handleReset() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择账号密码重置为ZhxN@1437?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return resetPassword(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleGrant() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.roleTreeObj = [];
      if (this.selectionList.length === 1) {
        this.roleTreeObj = this.selectionList[0].roleId.split(",");
      }
      getRoleTree().then((res) => {
        this.roleGrantList = res.data.data;
        this.roleBox = true;
      });
    },
    handleLock() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择账号解封？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return unlock(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    handleImport() {
      this.excelBox = true;
    },
    uploadAfter(res, done, loading, column) {
      window.console.log(column);
      this.excelBox = false;
      this.refreshChange();
      done();
    },
    handleExport() {
      const phone = func.toStr(this.search.phone);
      const realName = func.toStr(this.search.realName);
      const roleIds = func.toStr(this.search.roleIds);
      const deptId = func.toStr(this.treeGridId);
      this.$confirm("是否导出用户数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        NProgress.start();
        exportBlob(
          `/api/blade-system/user/export-user?${this.website.tokenHeader
          }=${getToken()}&phone=${phone}&realName=${realName}&roleIds=${roleIds}&deptId=${deptId}`
        ).then((res) => {
          downloadXls(res.data, `用户数据表${dateNow()}.xlsx`);
          NProgress.done();
        });
      });
    },
    handleTemplate() {
      exportBlob(
        `/api/blade-system/user/export-template?${this.website.tokenHeader
        }=${getToken()}`
      ).then((res) => {
        downloadXls(res.data, "用户数据模板.xlsx");
      });
    },
    beforeOpen(done, type, loading) {
      // loading()
      // console.log(type,["add"].includes(type),["edit", "view"].includes(type))
      if (["add"].includes(type)) {
        // loading()
        // this.getInitData(this.userInfo.tenant_id)
        done()
        // setTimeout(() => {
        //   done()
        // }, 500);
      }
      if (["edit", "view"].includes(type)) {
        loading()
        // this.getInitData(this.form.tenantId)
        getUser(this.form.id).then((res) => {
          // this.form = res.data.data;
          // if (this.form.hasOwnProperty("deptId")) {
          //   this.form.deptId = func.split(this.form.deptId);
          // }
          // if (this.form.hasOwnProperty("roleId")) {
          //   this.form.roleId = func.split(this.form.roleId);
          // }
          // this.$set(this.form, "phone",  res.data.data.phone)
          // this.$set(this.form, "roleId",  func.split( res.data.data.roleId))
          // if (this.form.hasOwnProperty("postId")) {
          //   this.form.postId = func.split(this.form.postId);
          // }
          // 等待两秒
          setTimeout(() => {
            this.form = res.data.data;
            if (this.form.hasOwnProperty("roleId")) {
              this.form.roleId = func.split(this.form.roleId);
            }
            this.$set(this.form, "phone", res.data.data.phone)
            this.$set(this.form, "roleId", func.split(res.data.data.roleId))
            done();
          }, 1500);

        }).finally(() => {
          // done()
        })
      }

    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query),
        this.treeGridId
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      })
        .finally(() => {
          this.loading = false;
        });
    },

    // roleIdChange(val){
    //   console.log(val);
    // },
  },
};
</script>

<style>
.box {
  height: 800px;
}

.el-scrollbar {
  height: 100%;
}

.box .el-scrollbar__wrap {
  overflow: scroll;
}
</style>




