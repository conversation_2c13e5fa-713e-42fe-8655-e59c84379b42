<!--
 * @Date: 2025-06-24 09:38:28
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 18:33:23
 * @Description:
 * @FilePath: \src\views\wel\index.vue
-->
<template>
  <div class="wel-dashboard">
    <!-- 顶部欢迎信息面板 -->
    <el-card class="info-panel-card" shadow="never">
      <div class="info-panel-flex">
        <el-avatar
          class="welcome-avatar"
          :size="56"
          fit="fill"
          :src="userInfo.avatar || ''"
          v-if="userInfo.avatar"
        />
        <el-avatar
          class="welcome-avatar"
          :size="56"
          v-else
        >{{ (userInfo.real_name || userInfo.nick_name || '用').charAt(0) }}</el-avatar>
        <div class="welcome-text-block">
          <div class="welcome-text">
            <span>欢迎回来，</span>
            <span class="welcome-name">{{ userInfo.real_name || userInfo.nick_name || '管理员' }}</span>！
          </div>
          <div class="welcome-time">{{ nowTime }}</div>
        </div>
        <div class="todo-info">
          <span class="todo-label">待办任务：</span>
          <span class="todo-count">{{ todoCount }}条</span>
        </div>
      </div>
    </el-card>

    <!-- 数据统计卡片区 -->
    <el-row :gutter="24" class="stat-row">
      <el-col :span="6" v-for="item in statList" :key="item.label">
        <el-card class="stat-card-simple" shadow="never">
          <div class="stat-value-simple">{{ item.value }}</div>
          <div class="stat-label-simple">{{ item.label }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <!-- <el-row :gutter="24" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">访问趋势</span>
          </div>
          <div class="chart-placeholder">[图表占位]</div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">用户增长</span>
          </div>
          <div class="chart-placeholder">[图表占位]</div>
        </el-card>
      </el-col>
    </el-row> -->

    <!-- 待办事项和最新公告区域 -->
    <el-row :gutter="24" class="content-row">
      <!-- 左侧：待办事项 -->
      <el-col :span="12">
        <el-card class="todo-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">待办事项</span>
            <span class="more-link" @click="showMoreTodo" >更多</span>
          </div>
          <el-tabs v-model="activeTodoTab" class="todo-tabs">
            <el-tab-pane label="工作" name="work">
              <el-table :data="todoWorkList" class="todo-table" :show-header="true">
                <el-table-column prop="name" label="事务名称" min-width="200">
                  <template slot-scope="scope">
                    <span class="todo-name">{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="source" label="来源" width="100">
                  <template slot-scope="scope">
                    <span class="todo-source">{{ scope.row.source }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="date" label="发布时间" width="120">
                  <template slot-scope="scope">
                    <span class="todo-date">{{ scope.row.date }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="预警" name="warning">
              <el-table :data="todoWarningList" class="todo-table" :show-header="true">
                <el-table-column prop="name" label="事务名称" min-width="200">
                  <template slot-scope="scope">
                    <span class="todo-name">{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="source" label="来源" width="100">
                  <template slot-scope="scope">
                    <span class="todo-source">{{ scope.row.source }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="date" label="发布时间" width="120">
                  <template slot-scope="scope">
                    <span class="todo-date">{{ scope.row.date }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>

      <!-- 右侧：最新公告 -->
      <el-col :span="12">
        <el-card class="notice-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">最新公告</span>
            <span class="more-link">更多</span>
          </div>
          <el-table :data="noticeList" class="notice-table" :show-header="true">
            <el-table-column prop="content" label="公告内容" min-width="200">
              <template slot-scope="scope">
                <span class="notice-content">{{ scope.row.content }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="100">
              <template slot-scope="scope">
                <span class="notice-source">{{ scope.row.source }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="发布时间" width="120">
              <template slot-scope="scope">
                <span class="notice-date">{{ scope.row.date }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 应用中心 -->
    <el-card class="app-center-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">常用应用</span>
      </div>

      <!-- 常用应用 -->
      <div class="app-section">
        <!-- <div class="app-section-title">常用应用</div> -->
        <div class="app-grid">
          <div
            class="app-item"
            v-for="(app) in displayApps"
            :key="app.id"
            @click="handleAppClick(app)"
          >
            <div class="app-icon">
              <img :src="app.iconUrl" alt="" class="app-image">
            </div>
            <div class="app-name">{{ app.name }}</div>
          </div>
          <div
            v-if="commonApps.length > maxDisplayApps"
            class="app-item more-item"
            @click="showAllApps"
          >
            <div class="app-icon more-icon">
              <i class="el-icon-more"></i>
            </div>
            <div class="app-name">更多</div>
          </div>
        </div>
      </div>

      <!-- 全部应用 -->
      <!-- <div class="app-section">
        <div class="app-section-title">全部应用</div>
        <div class="app-grid">
          <div
            class="app-item"
            v-for="app in allApps"
            :key="app.id"
            @click="handleAppClick(app)"
          >
            <div class="app-icon">
              <img :src="app.iconUrl" alt="" class="app-image">
            </div>
            <div class="app-name">{{ app.name }}</div>
          </div>
        </div>
      </div> -->
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { allApp,listCollect } from "@/api/appCenter/index";
export default {
  name: "wel",
  data() {
    return {
      todoCount: 3,
      activeTodoTab: 'work',
      statList: [
        { label: '用户总数', value: 12345 },
        { label: '今日访问量', value: 678 },
        { label: '累计访问量', value: 123456 },
        { label: '本月新增', value: 234 }
      ],
      todoWorkList: [
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' }
      ],
      todoWarningList: [
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { name: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' }
      ],
      noticeList: [
        { content: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { content: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { content: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { content: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' },
        { content: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', source: 'XXXXX', date: 'YYYY-MM-DD' }
      ],
      commonApps: [],
      allApps: [],
      nowTime: '',
      calendarDate: new Date(),
      maxDisplayApps: 8 // 一行显示的最大应用数量
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    // 显示的常用应用（限制数量）
    displayApps() {
      return this.commonApps.slice(0, this.maxDisplayApps);
    }
  },
  mounted() {
    this.updateTime();
    // console.log(this.userInfo);
    this._timer = setInterval(this.updateTime, 1000);
    this.init();
  },
  beforeDestroy() {
    clearInterval(this._timer);
  },
  methods: {
    init(){

      Promise.all([allApp(), listCollect()]).then(([allApp, commonApps]) => {
        this.allApps = allApp.data.data;
        this.commonApps = commonApps.data.data;
      });

    },
    updateTime() {
      const now = new Date();
      const pad = n => n < 10 ? '0' + n : n;
      this.nowTime = `${now.getFullYear()}-${pad(now.getMonth()+1)}-${pad(now.getDate())} ${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
    },
    // 处理应用点击事件
    handleAppClick(app) {
      if (app.jumpAddress) {
        window.open(app.jumpAddress, '_blank');
      }
    },
    // 显示所有应用
    showAllApps() {
      this.$router.push('/appCenter/index');
    },
    showMoreTodo() {
      this.$router.push('/todo/index');
    }
  }
};
</script>

<style scoped>
.wel-dashboard {
  /* padding: 24px; */
  /* background: #f8fafc; */
  min-height: 100vh;
  background: linear-gradient(252deg, #E8F1FF 0%, #F4F6FD 55.9%, #F2F9FD 68.94%, #F4F6FD 84.03%, #F0F4FF 100%);
}

.info-panel-card {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  background: #ffffff;
}

.info-panel-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
}

.welcome-avatar {
  margin-right: 24px;
  background: #419EFF;
  color: white;
  font-weight: 600;
}

.welcome-text-block {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.welcome-text {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.welcome-name {
  color: #419EFF;
  font-weight: 700;
}

.welcome-time {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.todo-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  background: #419EFF;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
}

.todo-label {
  margin-right: 6px;
}

.todo-count {
  font-weight: 600;
}
.stat-row {
  margin-bottom: 24px;
}

.stat-card-simple {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px 16px;
  text-align: center;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.stat-card-simple:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-value-simple {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8px;
}

.stat-label-simple {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.chart-row {
  margin-bottom: 24px;
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 320px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chart-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin: 16px;
  font-weight: 500;
  border: 2px dashed #d1d5db;
}

.content-row {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 !important;
  /* border-bottom: 1px solid #e2e8f0; */
  /* padding-bottom: 12px !important; */
  /* margin-bottom: 16px; */
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.more-link {
  font-size: 14px;
  color: #419EFF;
  cursor: pointer;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  background: #f1f5f9;
  transition: all 0.2s ease;
}

.more-link:hover {
  background: #e2e8f0;
  color: #2e7bff;
}

.content-row .el-col {
  display: flex;
}

.todo-card, .notice-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  border: 1px solid #e2e8f0;
  min-height: 450px;
  width: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
}

.todo-card:hover, .notice-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.todo-tabs {
  /* margin-top: 20px; */
  flex: 1;
  display: flex;
  flex-direction: column;
}

.todo-tabs .el-tabs__content {
  flex: 1;
}

.todo-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.todo-tabs .el-tabs__nav-wrap::after {
  background: #419EFF;
  height: 2px;
}

.todo-tabs .el-tabs__item {
  font-weight: 500;
  color: #64748b;
}

.todo-tabs .el-tabs__item.is-active {
  color: #419EFF;
  font-weight: 600;
}

.todo-table, .notice-table {
  background: transparent;
  border: none;
  flex: 1;
}

.todo-table .el-table__row, .notice-table .el-table__row {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.todo-table .el-table__row:hover, .notice-table .el-table__row:hover {
  background: #f8fafc;
}

.todo-table .el-table__header, .notice-table .el-table__header {
  background: #f9fafb;
  border-radius: 6px;
}

.todo-table th, .notice-table th {
  background: transparent !important;
  color: #374151 !important;
  font-weight: 600 !important;
  border: none !important;
}

/* 确保卡片内容区域能够撑满高度 */
.todo-card .el-card__body, .notice-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.todo-name, .notice-content {
  color: #1e293b;
  font-size: 14px;
  font-weight: 500;
}

.todo-source, .notice-source {
  color: #419EFF;
  font-size: 12px;
  font-weight: 500;
  background: #e6f4ff;
  padding: 4px 8px;
  border-radius: 6px;
}

.todo-date, .notice-date {
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
}

.app-center-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  border: 1px solid #e2e8f0;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.app-center-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.app-section {
  margin-bottom: 32px;
}

.app-section:last-child {
  margin-bottom: 0;
}

.app-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.app-item:hover {
  border-color: #419EFF;
  box-shadow: 0 4px 12px rgba(65, 158, 255, 0.15);
  transform: translateY(-2px);
}

.app-icon {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
}

.app-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.more-icon {
  background: #419EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.more-icon i {
  font-size: 24px;
  color: white;
}

.app-name {
  font-size: 12px;
  color: #1e293b;
  text-align: center;
  word-break: break-all;
  font-weight: 500;
  line-height: 1.2;
  max-height: 2.4em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>

