<!--
 * @Date: 2025-08-04 18:38:24
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 18:57:08
 * @Description: Tab切换页面
 * @FilePath: \src\views\wel\todoList.vue
-->
<template>
  <div class="tab-container">
    <!-- Tab导航栏 -->
    <div class="tab-nav">
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab1' }"
        @click="switchTab('tab1')"
      >
        工作待办
      </div>
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab2' }"
        @click="switchTab('tab2')"
      >
        预警待办
      </div>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <transition name="fade" mode="out-in">
        <TabComponent1 v-if="activeTab === 'tab1'" key="tab1" />
        <TabComponent2 v-if="activeTab === 'tab2'" key="tab2" />
      </transition>
    </div>
  </div>
</template>

<script>
import TabComponent1 from '@/views/digitalGovernance/mywork.vue'
import TabComponent2 from '@/components/TabComponent2.vue'

export default {
  name: 'TodoList',
  components: {
    TabComponent1,
    TabComponent2
  },
  data() {
    return {
      activeTab: 'tab1' // 默认激活第一个tab
    }
  },
  methods: {
    switchTab(tabName) {
      this.activeTab = tabName
    }
  }
}
</script>

<style scoped>
.tab-container {
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.page-header p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.tab-nav {
  display: flex;
  background: #f5f5f5;
  border-radius: 4px;
  padding: 2px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border-radius: 2px;
  color: #666;
  font-size: 14px;
  font-weight: normal;
}

.tab-item:hover {
  color: #333;
}

.tab-item.active {
  background: white;
  color: #333;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}



.tab-content {
  min-height: 500px;
}

/* 切换动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    padding: 15px;
  }

  .tab-nav {
    padding: 4px;
    border-radius: 10px;
  }

  .tab-item {
    padding: 10px 16px;
    flex-direction: column;
    gap: 4px;
    border-radius: 6px;
  }

  .tab-icon {
    margin-right: 0;
    margin-bottom: 2px;
    font-size: 18px;
  }

  .tab-text {
    font-size: 13px;
    font-weight: 600;
  }

  .page-header h1 {
    font-size: 24px;
  }
}
</style>