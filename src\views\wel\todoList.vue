<!--
 * @Date: 2025-08-04 18:38:24
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 18:53:55
 * @Description: Tab切换页面
 * @FilePath: \src\views\wel\todoList.vue
-->
<template>
  <div class="tab-container">
    <div class="page-header">
      <h1>Tab切换页面</h1>
      <p>这是一个演示tab切换功能的页面</p>
    </div>

    <!-- Tab导航栏 -->
    <div class="tab-nav">
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab1' }"
        @click="switchTab('tab1')"
      >
        <span class="tab-icon">📋</span>
        <span class="tab-text">组件1</span>
      </div>
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab2' }"
        @click="switchTab('tab2')"
      >
        <span class="tab-icon">📊</span>
        <span class="tab-text">组件2</span>
      </div>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <transition name="fade" mode="out-in">
        <TabComponent1 v-if="activeTab === 'tab1'" key="tab1" />
        <TabComponent2 v-if="activeTab === 'tab2'" key="tab2" />
      </transition>
    </div>
  </div>
</template>

<script>
import TabComponent1 from '@/views/digitalGovernance/mywork.vue'
import TabComponent2 from '@/components/TabComponent2.vue'

export default {
  name: 'TodoList',
  components: {
    TabComponent1,
    TabComponent2
  },
  data() {
    return {
      activeTab: 'tab1' // 默认激活第一个tab
    }
  },
  methods: {
    switchTab(tabName) {
      this.activeTab = tabName
    }
  }
}
</script>

<style scoped>
.tab-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.page-header p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.tab-nav {
  display: flex;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 6px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  border: 1px solid #e9ecef;
  position: relative;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  border-radius: 8px;
  position: relative;
  z-index: 2;
  color: #6c757d;
  font-weight: 500;
}

.tab-item:hover {
  color: #495057;
  background: rgba(255,255,255,0.5);
}

.tab-item.active {
  background: white;
  color: #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.15);
  transform: translateY(-1px);
}

.tab-item.active:hover {
  color: #0056b3;
  background: white;
}

.tab-icon {
  font-size: 20px;
  margin-right: 10px;
  transition: transform 0.3s ease;
}

.tab-item.active .tab-icon {
  transform: scale(1.1);
}

.tab-text {
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.tab-content {
  min-height: 500px;
}

/* 切换动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    padding: 15px;
  }

  .tab-nav {
    padding: 4px;
    border-radius: 10px;
  }

  .tab-item {
    padding: 10px 16px;
    flex-direction: column;
    gap: 4px;
    border-radius: 6px;
  }

  .tab-icon {
    margin-right: 0;
    margin-bottom: 2px;
    font-size: 18px;
  }

  .tab-text {
    font-size: 13px;
    font-weight: 600;
  }

  .page-header h1 {
    font-size: 24px;
  }
}
</style>