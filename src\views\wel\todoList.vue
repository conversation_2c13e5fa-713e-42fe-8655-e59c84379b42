<!--
 * @Date: 2025-08-04 18:38:24
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 18:49:09
 * @Description: Tab切换页面
 * @FilePath: \src\views\wel\todoList.vue
-->
<template>
  <div class="tab-container">
    <div class="page-header">
      <h1>Tab切换页面</h1>
      <p>这是一个演示tab切换功能的页面</p>
    </div>

    <!-- Tab导航栏 -->
    <div class="tab-nav">
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab1' }"
        @click="switchTab('tab1')"
      >
        <span class="tab-icon">📋</span>
        <span class="tab-text">组件1</span>
      </div>
      <div
        class="tab-item"
        :class="{ active: activeTab === 'tab2' }"
        @click="switchTab('tab2')"
      >
        <span class="tab-icon">📊</span>
        <span class="tab-text">组件2</span>
      </div>
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content">
      <transition name="fade" mode="out-in">
        <TabComponent1 v-if="activeTab === 'tab1'" key="tab1" />
        <TabComponent2 v-if="activeTab === 'tab2'" key="tab2" />
      </transition>
    </div>
  </div>
</template>

<script>
import TabComponent1 from '@/view/digitalGovernance/mywork.vue'
import TabComponent2 from '@/components/TabComponent2.vue'

export default {
  name: 'TodoList',
  components: {
    TabComponent1,
    TabComponent2
  },
  data() {
    return {
      activeTab: 'tab1' // 默认激活第一个tab
    }
  },
  methods: {
    switchTab(tabName) {
      this.activeTab = tabName
    }
  }
}
</script>

<style scoped>
.tab-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.page-header p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.tab-nav {
  display: flex;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item:hover {
  background: #e9ecef;
}

.tab-item.active {
  background: #007bff;
  color: white;
}

.tab-item.active:hover {
  background: #0056b3;
}

.tab-icon {
  font-size: 18px;
  margin-right: 8px;
}

.tab-text {
  font-size: 16px;
  font-weight: 500;
}

.tab-content {
  min-height: 500px;
}

/* 切换动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    padding: 15px;
  }

  .tab-item {
    padding: 12px 15px;
    flex-direction: column;
    gap: 5px;
  }

  .tab-icon {
    margin-right: 0;
    margin-bottom: 2px;
  }

  .tab-text {
    font-size: 14px;
  }

  .page-header h1 {
    font-size: 24px;
  }
}
</style>